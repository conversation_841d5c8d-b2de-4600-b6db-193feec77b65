(function (window, undefined) {
  // 全局变量
  let plugin = null;

  let currentCategory = '常用'; // 当前选中的分类
  let currentEditMode = 'visual'; // 当前编辑模式：'visual' 或 'latex'

  // 使用全局符号库
  const symbolLibrary = window.symbolLibrary || {};

  // 初始化插件
  function initPlugin() {
    try {
      // 等待 ONLYOFFICE API 加载
      if (typeof window.Asc !== 'undefined' && window.Asc.plugin) {
        plugin = window.Asc.plugin;
      }

      // 初始化 MathLive 编辑器
      initMathLiveEditor();

      // 动态创建工具栏
      createToolbar();

      // 添加矩阵对话框事件监听器
      addMatrixDialogEventListeners();

      // 添加清空按钮事件监听器
      addClearButtonEventListener();

      // 添加编辑模式切换事件监听器
      addModeSwitchEventListeners();

      // 默认使用可视化模式 初始化编辑模式
      switchToVisualMode();
    } catch (error) {}
  }
  // 初始化 MathLive 编辑器
  function initMathLiveEditor() {
    try {
      // 获取MathLive编辑器元素
      const mathField = document.getElementById('mathLive');
      mathField.mathVirtualKeyboardPolicy = 'sandboxed';
      // 添加焦点事件监听器
      mathField.addEventListener('focus', () => {
        mathVirtualKeyboard.visible = false;
        // 设置键盘插入到自定义容器中
        // mathVirtualKeyboard.container = document.getElementById('keyboard');
      });
      // 添加输入事件监听器
      // mathField.addEventListener('input', evt => {
      //     let regular = ensureFractionBraces(mathField.getValue());
      //     console.log('Value:', regular);
      // });

      // 为LaTeX编辑器添加输入事件监听器
      // const latexEditor = document.getElementById('latexEditor');
      // if (latexEditor) {
      //     latexEditor.addEventListener('input', evt => {
      //         let regular = ensureFractionBraces(latexEditor.value);
      //         console.log('LaTeX Value:', regular);
      //     });
      // }
    } catch (error) {
      console.error('MathLive 编辑器初始化失败:', error);
    }
  }

  // 动态创建工具栏
  function createToolbar() {
    const toolbarContainer = document.querySelector('.toolbar');
    const categoryTabsContainer = document.getElementById('category-tabs');
    const symbolContentContainer = document.getElementById('symbol-content');

    if (!toolbarContainer || !categoryTabsContainer || !symbolContentContainer) {
      console.error('找不到工具栏容器');
      return;
    }

    // 清空现有内容
    categoryTabsContainer.innerHTML = '';
    symbolContentContainer.innerHTML = '';

    // 创建分类标签
    Object.keys(symbolLibrary).forEach((categoryName, index) => {
      const categoryTab = document.createElement('div');
      categoryTab.className = 'category-tab';
      categoryTab.textContent = categoryName;
      categoryTab.setAttribute('data-category', categoryName);

      // 设置第一个分类为默认选中
      if (index === 0) {
        categoryTab.classList.add('active');
      }

      // 添加点击事件
      categoryTab.addEventListener('click', () => {
        switchCategory(categoryName);
      });

      categoryTabsContainer.appendChild(categoryTab);
    });

    // 显示默认分类的内容
    showCategoryContent(currentCategory);
  }

  // 切换分类
  function switchCategory(categoryName) {
    // 更新当前分类
    currentCategory = categoryName;

    // 更新标签状态
    const categoryTabs = document.querySelectorAll('.category-tab');
    categoryTabs.forEach(tab => {
      tab.classList.remove('active');
      if (tab.getAttribute('data-category') === categoryName) {
        tab.classList.add('active');
      }
    });

    // 显示对应分类的内容
    showCategoryContent(categoryName);
  }

  // 显示分类内容
  function showCategoryContent(categoryName) {
    const symbolContentContainer = document.getElementById('symbol-content');
    if (!symbolContentContainer) {
      console.error('找不到符号内容容器');
      return;
    }

    // 清空现有内容
    symbolContentContainer.innerHTML = '';

    // 获取当前分类的符号
    const symbols = symbolLibrary[categoryName];
    if (!symbols) {
      console.error('找不到分类:', categoryName);
      return;
    }

    // 检查是否是常用公式（有子分类）
    if (categoryName === '常用公式' && typeof symbols === 'object' && !Array.isArray(symbols)) {
      // 创建子标签页
      createFormulaSubTabs(symbols);
      return;
    }

    // 处理普通分类（数组格式）
    if (Array.isArray(symbols)) {
      createSymbolGroup(categoryName, symbols);
    }
  }

  // 创建常用公式的子标签页
  function createFormulaSubTabs(formulaCategories) {
    const symbolContentContainer = document.getElementById('symbol-content');

    // 创建子标签页容器
    const subTabsContainer = document.createElement('div');
    subTabsContainer.className = 'formula-sub-tabs';

    // 创建子标签页按钮
    const subTabNames = Object.keys(formulaCategories);
    subTabNames.forEach((subTabName, index) => {
      const subTab = document.createElement('div');
      subTab.className = 'formula-sub-tab';
      subTab.textContent = subTabName;

      if (index === 0) {
        subTab.classList.add('active');
      }

      subTab.addEventListener('click', () => {
        // 移除所有活动状态
        document.querySelectorAll('.formula-sub-tab').forEach(tab => {
          tab.classList.remove('active');
        });

        // 设置当前活动状态
        subTab.classList.add('active');

        // 显示对应的公式
        showFormulaSubCategory(subTabName, formulaCategories[subTabName]);
      });

      subTabsContainer.appendChild(subTab);
    });

    symbolContentContainer.appendChild(subTabsContainer);

    // 显示第一个子分类的内容
    if (subTabNames.length > 0) {
      showFormulaSubCategory(subTabNames[0], formulaCategories[subTabNames[0]]);
    }
  }

  // 显示公式子分类内容
  function showFormulaSubCategory(subCategoryName, formulas) {
    // 移除之前的公式内容
    const existingContent = document.querySelector('.formula-content');
    if (existingContent) {
      existingContent.remove();
    }

    // 创建公式内容容器
    const formulaContent = document.createElement('div');
    formulaContent.className = 'formula-content';

    // 创建公式按钮
    formulas.forEach(formulaData => {
      const button = document.createElement('button');
      button.setAttribute('data-symbol', formulaData.symbol);
      button.textContent = formulaData.label;
      // button.title = formulaData.label;

      // 添加悬停效果
      button.addEventListener('mouseenter', () => {
        showFormulaPreview(button, formulaData.symbol, formulaData.label);
      });

      button.addEventListener('mouseleave', () => {
        hideFormulaPreview();
      });

      // 添加点击事件
      button.addEventListener('click', () => {
        insertSymbol(formulaData.symbol);
      });

      formulaContent.appendChild(button);
    });

    document.getElementById('symbol-content').appendChild(formulaContent);
  }

  // 创建普通符号组
  function createSymbolGroup(categoryName, symbols) {
    const symbolContentContainer = document.getElementById('symbol-content');

    // 创建符号组
    const symbolGroup = document.createElement('div');
    symbolGroup.className = 'symbol-group';

    // 创建符号按钮
    symbols.forEach(symbolData => {
      const button = document.createElement('button');
      button.setAttribute('data-symbol', symbolData.symbol);
      button.textContent = '';
      if (symbolData.position) {
        button.title = `${symbolData.label}`;
        const position = symbolData.position.split(',');
        button.style.backgroundImage = `url('./resources/img/iconss.png')`;
        button.style.backgroundPosition = `${position[0]}px ${position[1]}px`;
      } else {
        button.textContent = `${symbolData.label}`;
        button.style.width = 'auto';
        button.style.padding = '0 10px';
        // 添加悬停预览功能
        button.addEventListener('mouseenter', () => {
          showFormulaPreview(button, symbolData.symbol, symbolData.label);
        });

        button.addEventListener('mouseleave', () => {
          hideFormulaPreview();
        });
      }
      // 添加点击事件
      button.addEventListener('click', () => {
        insertSymbol(symbolData.symbol);
      });

      symbolGroup.appendChild(button);
    });

    symbolContentContainer.appendChild(symbolGroup);
  }

  // 插入符号到编辑器
  function insertSymbol(symbol) {
    try {
      // 检查是否是特殊对话框符号
      if (symbol === 'MATRIX_DIALOG') {
        openMatrixDialog();
        return;
      }

      // 插入符号到当前编辑器
      if (currentEditMode === 'visual') {
        const mathField = document.getElementById('mathLive');
        if (mathField && mathField.insert) {
          mathField.insert(symbol);
        }
      } else {
        // LaTeX模式下，去除placeholder
        const processedSymbol = removePlaceholders(symbol);
        const latexEditor = document.getElementById('latexEditor');
        if (latexEditor) {
          const currentValue = latexEditor.value;
          const cursorPos = latexEditor.selectionStart;
          const newValue = currentValue.slice(0, cursorPos) + processedSymbol + currentValue.slice(cursorPos);
          latexEditor.value = newValue;
          // 设置光标位置
          latexEditor.setSelectionRange(cursorPos + processedSymbol.length, cursorPos + processedSymbol.length);
          latexEditor.focus();
        }
      }
    } catch (error) {
      console.error('插入符号失败:', error);
    }
  }

  // 显示公式预览
  function showFormulaPreview(button, symbol, label) {
    // 移除已存在的预览
    hideFormulaPreview();

    // 创建预览容器
    const preview = document.createElement('div');
    preview.id = 'formula-preview';

    // 创建预览标题
    const title = document.createElement('div');
    title.className = 'title';
    title.textContent = label;
    preview.appendChild(title);

    // 创建MathLive预览区域
    const mathPreview = document.createElement('math-field');
    mathPreview.setAttribute('read-only', 'true');
    mathPreview.setAttribute('default-mode', 'inline-math');
    mathPreview.style.cssText = `
                width: 100%;
                min-height: 40px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: #f8f9fa;
                font-size: 1.2em;
            `;
    preview.appendChild(mathPreview);

    // 添加到页面
    document.body.appendChild(preview);

    // 设置预览内容
    if (mathPreview.setValue) {
      mathPreview.setValue(symbol);
    }

    // 计算位置
    const buttonRect = button.getBoundingClientRect();
    const previewRect = preview.getBoundingClientRect();
    const windowWidth = window.innerWidth;

    // 默认显示在按钮下方
    let left = buttonRect.left + buttonRect.width / 2 - previewRect.width / 2;
    let top = buttonRect.bottom + 10;

    // 如果预览超出右边界，调整位置
    if (left + previewRect.width > windowWidth - 100) {
      left = windowWidth - previewRect.width - 180;
    }

    // 如果预览超出左边界，调整位置
    if (left < 10) {
      left = 10;
    }

    // 如果预览超出下边界，显示在按钮上方
    if (top + previewRect.height > window.innerHeight) {
      top = buttonRect.top - previewRect.height - 10;
    }

    preview.style.left = left + 'px';
    preview.style.top = top + 'px';
  }

  // 隐藏公式预览
  function hideFormulaPreview() {
    const existingPreview = document.getElementById('formula-preview');
    if (existingPreview) {
      existingPreview.remove();
    }
  }
  // 手动处理LaTeX，确保分式有花括号
  function ensureFractionBraces(latex) {
    if (!latex) return latex;
    // 匹配 \frac{...}{...} 格式
    latex = latex.replace(/\\frac\{([^}]+)\}\{([^}]+)\}/g, (match, numerator, denominator) => {
      return `\\frac{${numerator}}{${denominator}}`;
    });
    // 匹配 \frac数字数字 格式（如 \frac15）
    latex = latex.replace(/\\frac(\d+)(\d+)/g, (match, numerator, denominator) => {
      return `\\frac{${numerator}}{${denominator}}`;
    });
    // 匹配 \frac{数字}数字 格式
    latex = latex.replace(/\\frac\{(\d+)\}(\d+)/g, (match, numerator, denominator) => {
      return `\\frac{${numerator}}{${denominator}}`;
    });
    // 匹配 \frac数字{数字} 格式
    latex = latex.replace(/\\frac(\d+)\{(\d+)\}/g, (match, numerator, denominator) => {
      return `\\frac{${numerator}}{${denominator}}`;
    });
    return latex;
  }

  // 去除空的{}括号（只保留首位的）
  function removeEmptyBraces(latex) {
    if (!latex) return latex;
    // 去除所有空的{}，但保留首位的{}
    return latex.replace(/(?<!^)\{\s*\}/g, '');
  }

  // 去除LaTeX中的placeholder
  function removePlaceholders(latex) {
    if (!latex) return latex;

    // 去除 \placeholder{}
    latex = latex.replace(/\\placeholder\{\}/g, '');

    // 去除 \placeholder{内容} 但保留内容
    latex = latex.replace(/\\placeholder\{([^}]*)\}/g, '$1');

    // 清理可能产生的多余空格和分隔符
    latex = latex.replace(/\s*&\s*&\s*/g, ' & '); // 清理矩阵中的多余&
    latex = latex.replace(/\s*\\\\\s*\\\\\s*/g, ' \\\\ '); // 清理多余的行分隔符

    return latex;
  }

  // 智能清理多余的placeholder
  function cleanRedundantPlaceholders(latex) {
    if (!latex) return latex;

    // 清理连续的placeholder
    latex = latex.replace(/\\placeholder\{\}\s*\\placeholder\{\}/g, '\\placeholder{}');

    // 清理在不需要placeholder的上下文中的placeholder
    // 例如：在普通文本中的单独placeholder，但保留矩阵中的placeholder
    latex = latex.replace(/(?<!\\)\\placeholder\{\}(?![\s\w\\&])/g, '');

    // 注意：不再清理矩阵中的placeholder，因为矩阵中的空单元格需要placeholder供用户编辑

    return latex;
  }

  // 检查是否还有未填写的placeholder
  function hasUnfilledPlaceholders(latex) {
    if (!latex) return false;

    // 检查是否存在空的placeholder（\placeholder{}）
    const hasEmptyPlaceholders = /\\placeholder\{\}/.test(latex);

    // 检查是否存在只有空格的placeholder（\placeholder{ }）
    const hasWhitespaceOnlyPlaceholders = /\\placeholder\{\s+\}/.test(latex);

    return hasEmptyPlaceholders || hasWhitespaceOnlyPlaceholders;
  }

  // 恢复LaTeX中的placeholder（用于切换到可视化模式）
  function restorePlaceholders(latex) {
    if (!latex) return latex;

    // 首先清理可能存在的多余placeholder，但保留矩阵中的placeholder
    latex = cleanRedundantPlaceholders(latex);

    // 特别处理矩阵：确保矩阵中的空单元格有placeholder
    latex = restoreMatrixPlaceholders(latex);

    // 只对真正需要placeholder的结构进行添加，避免重复添加
    const patterns = [
      // 分数
      { pattern: /\\frac\{\}\{\}/g, replacement: '\\frac{\\placeholder{}}{\\placeholder{}}' },
      // 根号
      { pattern: /\\sqrt\{\}/g, replacement: '\\sqrt{\\placeholder{}}' },
      { pattern: /\\sqrt\[\]\{\}/g, replacement: '\\sqrt[\\placeholder{}]{\\placeholder{}}' },
      // 上标下标
      { pattern: /\^\{\}/g, replacement: '^{\\placeholder{}}' },
      { pattern: /_\{\}/g, replacement: '_{\\placeholder{}}' },
      // 括号
      { pattern: /\\left\(\\right\)/g, replacement: '\\left(\\placeholder{}\\right)' },
      { pattern: /\\left\|\\right\|/g, replacement: '\\left|\\placeholder{}\\right|' },
      { pattern: /\\left\[\\right\]/g, replacement: '\\left[\\placeholder{}\\right]' },
      { pattern: /\\left\\{\\right\\}/g, replacement: '\\left\\{\\placeholder{}\\right\\}' },
      // 积分
      { pattern: /\\int_\{\}/g, replacement: '\\int_{\\placeholder{}}' },
      { pattern: /\\int_\{\}\^\{\}/g, replacement: '\\int_{\\placeholder{}}^{\\placeholder{}}' },
      // 求和求积
      { pattern: /\\sum_\{\}/g, replacement: '\\sum_{\\placeholder{}}' },
      { pattern: /\\sum_\{\}\^\{\}/g, replacement: '\\sum_{\\placeholder{}}^{\\placeholder{}}' },
      { pattern: /\\prod_\{\}/g, replacement: '\\prod_{\\placeholder{}}' },
      { pattern: /\\prod_\{\}\^\{\}/g, replacement: '\\prod_{\\placeholder{}}^{\\placeholder{}}' },
      // 极限
      { pattern: /\\lim_\{\}/g, replacement: '\\lim_{\\placeholder{}}' },
      // 微分
      { pattern: /\\operatorname d\{\}/g, replacement: '\\operatorname d\\placeholder{}' },
      {
        pattern: /\\frac\{\\operatorname d\}\{\}/g,
        replacement: '\\frac{\\operatorname d\\placeholder{}}{\\operatorname d\\placeholder{}}',
      },
      { pattern: /\\frac\{\\partial\}\{\}/g, replacement: '\\frac{\\partial\\placeholder{}}{\\partial\\placeholder{}}' },
    ];

    // 按顺序应用模式，避免重复处理
    patterns.forEach(({ pattern, replacement }) => {
      latex = latex.replace(pattern, replacement);
    });

    return latex;
  }

  // 恢复矩阵中的placeholder
  function restoreMatrixPlaceholders(latex) {
    if (!latex) return latex;

    // 匹配各种矩阵环境
    const matrixPatterns = [
      /\\begin\{matrix\}(.*?)\\end\{matrix\}/g,
      /\\begin\{pmatrix\}(.*?)\\end\{pmatrix\}/g,
      /\\begin\{bmatrix\}(.*?)\\end\{bmatrix\}/g,
      /\\begin\{vmatrix\}(.*?)\\end\{vmatrix\}/g,
      /\\begin\{Vmatrix\}(.*?)\\end\{Vmatrix\}/g,
      /\\begin\{Bmatrix\}(.*?)\\end\{Bmatrix\}/g,
    ];

    matrixPatterns.forEach(pattern => {
      latex = latex.replace(pattern, (match, content) => {
        // 在矩阵内容中，为空的单元格添加placeholder
        // 处理 & 分隔的单元格
        let processedContent = content.replace(/&\s*&/g, '&\\placeholder{}&');
        processedContent = processedContent.replace(/&\s*\\\\/g, '&\\placeholder{}\\\\');
        processedContent = processedContent.replace(/\\\\\s*&/g, '\\\\&\\placeholder{}');

        // 处理行首和行尾的空单元格
        processedContent = processedContent.replace(/^&\s*/g, '\\placeholder{}&');
        processedContent = processedContent.replace(/\s*&$/g, '&\\placeholder{}');

        return match.replace(content, processedContent);
      });
    });

    return latex;
  }

  // 矩阵对话框相关函数
  function addMatrixDialogEventListeners() {
    // 关闭按钮
    const closeBtn = document.getElementById('close-matrix-dialog');
    const cancelBtn = document.getElementById('cancel-matrix');
    const insertBtn = document.getElementById('insert-matrix');

    if (closeBtn) {
      closeBtn.addEventListener('click', closeMatrixDialog);
    }

    if (cancelBtn) {
      cancelBtn.addEventListener('click', closeMatrixDialog);
    }

    if (insertBtn) {
      insertBtn.addEventListener('click', insertMatrix);
    }

    // 配置变化时更新预览
    const rowsSelect = document.getElementById('matrix-rows');
    const colsSelect = document.getElementById('matrix-cols');
    const bracketsSelect = document.getElementById('matrix-brackets');

    if (rowsSelect) {
      rowsSelect.addEventListener('change', updateMatrixPreview);
    }

    if (colsSelect) {
      colsSelect.addEventListener('change', updateMatrixPreview);
    }

    if (bracketsSelect) {
      bracketsSelect.addEventListener('change', updateMatrixPreview);
    }

    // 点击对话框背景关闭
    const dialog = document.getElementById('matrix-dialog');
    if (dialog) {
      dialog.addEventListener('click', function (e) {
        if (e.target === dialog) {
          closeMatrixDialog();
        }
      });
    }
  }

  // 打开矩阵对话框
  function openMatrixDialog() {
    const dialog = document.getElementById('matrix-dialog');
    if (dialog) {
      dialog.style.display = 'flex';
      updateMatrixPreview();
    }
  }

  // 关闭矩阵对话框
  function closeMatrixDialog() {
    const dialog = document.getElementById('matrix-dialog');
    if (dialog) {
      dialog.style.display = 'none';
    }
  }

  // 生成矩阵LaTeX代码
  function generateMatrixLatex(rows, cols, bracketType, includePlaceholders = true) {
    let matrixContent = '';

    // 生成矩阵内容
    for (let i = 0; i < rows; i++) {
      const row = [];
      for (let j = 0; j < cols; j++) {
        // 根据参数决定是否包含placeholder
        if (includePlaceholders) {
          row.push('\\placeholder{}');
        } else {
          row.push(''); // 空内容，用户可以直接输入
        }
      }
      matrixContent += row.join(' & ');
      if (i < rows - 1) {
        matrixContent += ' \\\\ ';
      }
    }

    // 根据括号类型生成完整的LaTeX代码
    let latexCode = '';
    switch (bracketType) {
      case 'matrix':
        latexCode = `\\begin{matrix}${matrixContent}\\end{matrix}`;
        break;
      case 'pmatrix':
        latexCode = `\\begin{pmatrix}${matrixContent}\\end{pmatrix}`;
        break;
      case 'bmatrix':
        latexCode = `\\begin{bmatrix}${matrixContent}\\end{bmatrix}`;
        break;
      case 'vmatrix':
        latexCode = `\\begin{vmatrix}${matrixContent}\\end{vmatrix}`;
        break;
      case 'Vmatrix':
        latexCode = `\\begin{Vmatrix}${matrixContent}\\end{Vmatrix}`;
        break;
      case 'Bmatrix':
        latexCode = `\\begin{Bmatrix}${matrixContent}\\end{Bmatrix}`;
        break;
      default:
        latexCode = `\\begin{pmatrix}${matrixContent}\\end{pmatrix}`;
    }

    return latexCode;
  }

  // 更新矩阵预览
  function updateMatrixPreview() {
    const rowsSelect = document.getElementById('matrix-rows');
    const colsSelect = document.getElementById('matrix-cols');
    const bracketsSelect = document.getElementById('matrix-brackets');
    const previewContent = document.getElementById('matrix-preview-content');

    if (!rowsSelect || !colsSelect || !bracketsSelect || !previewContent) {
      return;
    }

    const rows = parseInt(rowsSelect.value);
    const cols = parseInt(colsSelect.value);
    const bracketType = bracketsSelect.value;

    const latexCode = generateMatrixLatex(rows, cols, bracketType);

    // 清空预览内容
    previewContent.innerHTML = '';

    // 创建预览用的MathLive元素
    const mathPreview = document.createElement('math-field');
    mathPreview.setAttribute('readonly', 'true');
    mathPreview.setAttribute('default-mode', 'display-math');
    mathPreview.style.cssText = `
            width: 100%;
            min-height: 40px;
            border: none;
            background: transparent;
            font-size: 1.2em;
        `;

    previewContent.appendChild(mathPreview);

    // 设置预览内容
    if (mathPreview.setValue) {
      mathPreview.setValue(latexCode);
    }
  }

  // 插入矩阵到编辑器
  function insertMatrix() {
    const rowsSelect = document.getElementById('matrix-rows');
    const colsSelect = document.getElementById('matrix-cols');
    const bracketsSelect = document.getElementById('matrix-brackets');

    if (!rowsSelect || !colsSelect || !bracketsSelect) {
      return;
    }

    const rows = parseInt(rowsSelect.value);
    const cols = parseInt(colsSelect.value);
    const bracketType = bracketsSelect.value;

    // 根据当前编辑模式决定是否包含placeholder
    const includePlaceholders = currentEditMode === 'visual';
    const latexCode = generateMatrixLatex(rows, cols, bracketType, includePlaceholders);

    // 插入到编辑器
    insertSymbol(latexCode);

    // 关闭对话框
    closeMatrixDialog();
  }

  // 添加清空按钮事件监听器
  function addClearButtonEventListener() {
    const clearBtn = document.getElementById('clear-btn');
    if (clearBtn) {
      clearBtn.addEventListener('click', clearEditor);
    }
  }

  // 清空编辑器内容
  function clearEditor() {
    try {
      if (currentEditMode === 'visual') {
        if (typeof mathLive !== 'undefined' && mathLive.setValue) {
          mathLive.setValue('');
        }
      } else {
        const latexEditor = document.getElementById('latexEditor');
        if (latexEditor) {
          latexEditor.value = '';
        }
      }
    } catch (error) {
      console.error('清空编辑器失败:', error);
    }
  }

  // 添加编辑模式切换事件监听器
  function addModeSwitchEventListeners() {
    const visualModeBtn = document.getElementById('visual-mode-btn');
    const latexModeBtn = document.getElementById('latex-mode-btn');

    if (visualModeBtn) {
      visualModeBtn.addEventListener('click', () => {
        switchToVisualMode();
      });
    }

    if (latexModeBtn) {
      latexModeBtn.addEventListener('click', () => {
        switchToLatexMode();
      });
    }
  }

  // 切换到可视化编辑模式
  function switchToVisualMode() {
    if (currentEditMode === 'visual') return;

    try {
      // 保存当前LaTeX内容
      const latexEditor = document.getElementById('latexEditor');
      const mathField = document.getElementById('mathLive');

      if (latexEditor && mathField) {
        let latexContent = latexEditor.value;

        // 在切换到可视化模式时，需要恢复placeholder以便MathLive正确显示
        latexContent = restorePlaceholders(latexContent);

        // 切换到可视化编辑器
        latexEditor.style.display = 'none';
        mathField.style.display = 'block';

        // 设置MathLive内容
        if (mathField.setValue) {
          mathField.setValue(latexContent);
        }
      }

      // 更新按钮状态
      updateModeButtons('visual');
      currentEditMode = 'visual';

      // 保存模式状态
      localStorage.setItem('mathLiveEditMode', 'visual');
    } catch (error) {
      console.error('切换到可视化模式失败:', error);
    }
  }

  // 切换到LaTeX编辑模式
  function switchToLatexMode() {
    if (currentEditMode === 'latex') return;

    try {
      const mathField = document.getElementById('mathLive');
      const latexEditor = document.getElementById('latexEditor');

      if (mathField && latexEditor) {
        // 获取当前MathLive内容
        let latexContent = mathField.getValue ? ensureFractionBraces(mathField.getValue()) : '';

        // 检查是否还有未填写的placeholder
        if (hasUnfilledPlaceholders(latexContent)) {
          showMessage('请先填写完所有占位符内容后再切换到LaTeX模式', 'warning');
          return;
        }

        // 在切换到LaTeX模式时，去除placeholder
        latexContent = removePlaceholders(latexContent);

        // 切换到LaTeX编辑器
        mathField.style.display = 'none';
        latexEditor.style.display = 'block';

        // 设置LaTeX内容
        latexEditor.value = latexContent;

        // 聚焦到LaTeX编辑器
        latexEditor.focus();
      }

      // 更新按钮状态
      updateModeButtons('latex');
      currentEditMode = 'latex';
    } catch (error) {
      console.error('切换到LaTeX模式失败:', error);
    }
  }

  // 更新模式按钮状态
  function updateModeButtons(activeMode) {
    const visualModeBtn = document.getElementById('visual-mode-btn');
    const latexModeBtn = document.getElementById('latex-mode-btn');

    if (visualModeBtn && latexModeBtn) {
      if (activeMode === 'visual') {
        visualModeBtn.classList.add('active');
        latexModeBtn.classList.remove('active');
      } else {
        visualModeBtn.classList.remove('active');
        latexModeBtn.classList.add('active');
      }
    }
  }

  // 获取当前编辑器的内容
  function getCurrentEditorValue() {
    if (currentEditMode === 'visual') {
      const mathField = document.getElementById('mathLive');
      return mathField && mathField.getValue ? mathField.getValue() : '';
    } else {
      const latexEditor = document.getElementById('latexEditor');
      return latexEditor ? latexEditor.value : '';
    }
  }

  // 设置当前编辑器的内容
  function setCurrentEditorValue(value) {
    if (currentEditMode === 'visual') {
      const mathField = document.getElementById('mathLive');
      if (mathField && mathField.setValue) {
        mathField.setValue(value);
      }
    } else {
      const latexEditor = document.getElementById('latexEditor');
      if (latexEditor) {
        latexEditor.value = value;
      }
    }
  }

  // 获取LaTeX代码
  function getLatexCode() {
    try {
      const latexCode = getCurrentEditorValue();
      let latex = ensureFractionBraces(latexCode);
      // 去除空的{}括号
      latex = removeEmptyBraces(latex);
      // 检查是否包含不允许的样式命令
      const styleCommands = ['textstyle', 'displaystyle', 'scriptstyle'];
      const foundStyleCommand = styleCommands.find(cmd => latex.includes(cmd));

      if (foundStyleCommand) {
        showMessage(`公式中不允许使用\\ ${foundStyleCommand} 命令，已切换到LaTeX模式供您检查编辑`, 'error');
        // 自动切换到LaTeX模式
        switchToLatexMode();
        return;
      }

      // 检查是否还有未填写的placeholder
      if (hasUnfilledPlaceholders(latex)) {
        showMessage('请先填写完所有占位符内容在插入到文档', 'warning');
        return;
      }
      localStorage.setItem('currentFormulaToInsert', latex);
      if (plugin) {
        insertToWordDocument();
      }
    } catch (error) {
      console.error('获取LaTeX代码失败:', error);
    }
  }

  // 插入公式到文档
  function insertToWordDocument() {
    window.Asc.plugin.callCommand(
      function () {
        function getCurrentParagraphPosition() {
          var oDocument = Api.GetDocument();
          var currentSentence = oDocument.GetCurrentSentence() || '';
          var searchStr = '^$' + currentSentence;
          oDocument.ReplaceCurrentSentence(searchStr);
          var targetPosition = -1;
          for (var i = 0; i < oDocument.GetElementsCount(); i++) {
            var element = oDocument.GetElement(i);
            if (element.GetClassType() === 'paragraph') {
              var oText = element.GetText().trim();
              if (oText.includes(searchStr.trim())) {
                targetPosition = i;
                oDocument.ReplaceCurrentSentence(currentSentence);
                break;
              }
            }
          }
          return targetPosition;
        }

        let targetPosition = getCurrentParagraphPosition();
        console.log('targetPosition', targetPosition);

        // 从本地缓存读取公式
        var cachedFormula = localStorage.getItem('currentFormulaToInsert');
        var oDocument = Api.GetDocument();

        // if (oDocument) {
        // oDocument.AddMathEquation(cachedFormula ? cachedFormula : '', "latex");
        // }

        // // 检查当前行是否为空白行
        // var currentSentence = oDocument.GetCurrentSentence() || '';
        // var currentParagraph = oDocument.GetCurrentParagraph();
        // var currentParagraphText = currentParagraph ? currentParagraph.GetText().trim() : '';

        // // 如果当前行不是空白行，给出提示并返回
        // if (currentParagraphText && currentParagraphText !== '') {
        //     return { error: '公式只能插入在空白行，请将光标移动到空白行后再插入公式' };
        // }

        // // 先计算当前已有的公式数量
        // var allParagraphs = oDocument.GetAllParagraphs();
        // var existingFormulaCount = 0;
        // for (var i = 0; i < allParagraphs.length; i++) {
        //     var paragraphText = allParagraphs[i].GetText();
        //     // 检查是否是公式段落（包含数学公式编号）
        //     if (paragraphText.includes('(') && paragraphText.includes(')') &&
        //         paragraphText.match(/\(\d+\)/g)) {
        //         existingFormulaCount++;
        //     }
        // }

        // var currentFormulaNumber = existingFormulaCount + 1;
        // console.log('找到', existingFormulaCount, '个已有公式段落，新公式编号为：', currentFormulaNumber);

        // 先插入数学公式到当前光标位置
        // oDocument.AddMathEquation(cachedFormula, "latex");
        let doc = Api.GetDocument();
        let nParagraph = Api.CreateParagraph();
        let paragraph = doc.GetElement(0);
        let inlineLvlSdt = Api.CreateInlineLvlSdt();
        let run = Api.CreateRun();
        run.AddText('This is an inline text content control.');
        inlineLvlSdt.AddElement(run, 0);
        paragraph.AddInlineLvlSdt(inlineLvlSdt);
        oDocument.AddElement(targetPosition + 1, nParagraph);

        // var allParagraphsAfterInsert = oDocument.GetAllParagraphs();
        // var insertedFormulaParagraph = allParagraphsAfterInsert[allParagraphsAfterInsert.length - 1];

        // // 设置公式段落样式
        // var formulaStyle = oDocument.GetStyle('标准文件_正文公式');
        // if (formulaStyle) {
        //     insertedFormulaParagraph.SetStyle(formulaStyle);
        // }

        // // 设置段落样式，实现公式居中效果
        // var paraPr = insertedFormulaParagraph.GetParaPr();
        // if (paraPr) {
        //     // 设置段落居中对齐
        //     paraPr.SetJc('center');

        //     try {
        //         if (paraPr.SetTabStops && typeof paraPr.SetTabStops === 'function') {
        //             // 设置右对齐虚线制表符，用于公式编号
        //             paraPr.SetTabStops([Api.CreateTabStop('right', 0, 'dot')]);
        //         }
        //     } catch (tabError) {
        //         console.log('制表符设置失败，使用默认制表符：', tabError.message);
        //     }
        // }

        // 在公式段落末尾添加制表符和公式编号
        // insertedFormulaParagraph.AddTabStop();
        // var numberRun = Api.CreateRun();
        // numberRun.AddText('(' + currentFormulaNumber + ')');
        // insertedFormulaParagraph.AddElement(numberRun);
        // 创建"式中"段落
        // try {
        //   let oDocument = Api.GetDocument();
        //   let nParagraph = Api.CreateParagraph();
        //   nParagraph.AddText('式中');
        //   nParagraph.SetStyle(oDocument.GetStyle('标准文件_段'));
        //   oDocument.AddElement(targetPosition + 1, nParagraph);
        // } catch (error) {
        //   console.log(error);
        // }
        // 创建说明段落（带下划线）
        // var descParagraph = Api.CreateParagraph();
        // var descRun = Api.CreateRun();
        // descRun.AddText('——');
        // descRun.AddLineBreak();
        // descParagraph.AddElement(descRun);
        // descParagraph.SetStyle(oDocument.GetStyle('标准文件_标准正文'));
        // oDocument.AddElement(targetPosition+2,descParagraph);

        // 使用 ApiParagraph 的 InsertParagraph 方法在公式段落后插入段落
        // 根据 ONLYOFFICE API 文档，这是正确的方法
        // insertedFormulaParagraph.InsertParagraph(whereParagraph, 'after');
        // whereParagraph.InsertParagraph(descParagraph, 'after');
        // oDocument.InsertContent([insertedFormulaParagraph, whereParagraph, descParagraph], false);

        console.log('插入公式成功，编号1234566666：');
        // 插入成功后清除缓存
        localStorage.removeItem('currentFormulaToInsert');
      },
      false,
      true,
      function (result) {
        if (result && result.error) {
          showMessage('插入公式失败：' + result.error, 'error');
        } else {
          closePlugin();
        }
      }
    );
  }
  // 显示消息
  function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
      messageDiv.style.animation = 'slideOut 0.3s ease';
      if (messageDiv.parentNode) {
        messageDiv.parentNode.removeChild(messageDiv);
      }
    }, 3500);
  }
  // 关闭插件
  function closePlugin() {
    if (window.Asc && window.Asc.plugin && window.Asc.plugin.executeCommand) {
      window.Asc.plugin.executeCommand('close', '');
    } else {
      // 无法关闭插件：插件环境未正确初始化
    }
  }

  // 插件初始化
  if (window.Asc && window.Asc.plugin) {
    window.Asc.plugin.init = function () {
      try {
        initPlugin();
      } catch (error) {
        showMessage('插件初始化失败: ' + error.message, 'error');
      }
    };

    // 插件按钮事件
    window.Asc.plugin.button = function (id) {
      id === 1 ? getLatexCode() : closePlugin();
    };

    // 插件工具栏菜单点击事件
    window.Asc.plugin.event_onToolbarMenuClick = function (event) {};

    // 插件关闭时的清理工作
    window.Asc.plugin.onExternalMouseUp = function () {};
  } else {
    // 尝试独立运行模式
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', function () {
        setTimeout(initPlugin, 100);
      });
    } else {
      setTimeout(initPlugin, 100);
    }
  }
})(window, undefined);
